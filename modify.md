# KResearch 优化方案：提升报告与主题的关联性

## 1. 问题分析

当前 `KResearch` 和 `deep-research` 项目都面临一个共性问题：在深度研究后，最终生成的报告内容可能与用户最初提出的核心问题存在偏差（“偏题”）。

根本原因分析如下：

1.  **上下文漂移 (Context Drift)**: 在多轮迭代的搜索和阅读过程中，AI 的注意力可能会被新发现的、有趣的、但非核心的细节所吸引，导致后续的研究方向逐渐偏离原始主轴。
2.  **动态规划的副作用**: `KResearch` 的双智能体辩论规划非常灵活和强大，但也因为其高度的动态性，缺乏一个“顶层设计”或“最终蓝图”作为强约束。这使得智能体在“战术”层面上的自由发挥有时会凌驾于“战略”目标之上。
3.  **综合阶段的信息过载**: 在最后的报告生成阶段，AI 需要处理大量零散的“学习点”。如果没有一个清晰、强制性的结构指引，AI 可能会自行决定内容的组织方式和详略程度，从而导致重点模糊或偏离。

## 2. 核心解决思路：融合与增强

为了解决上述问题，我们建议将 `deep-research` 项目中**结构化规划**的优点，与 `KResearch` 项目中**动态迭代研究**的优点相结合，形成一套新的、更稳健的研究流程。

核心思路是：在 `KResearch` 的研究流程中引入一个**“研究大纲” (Research Outline)** 的概念。这个大纲在研究正式开始前生成，并作为整个后续流程（包括规划、搜索、阅读、综合）的**“宪法”**，确保所有步骤都服务于这个既定框架。

## 3. 详细修改方案

我们将在 `KResearch` 现有的流程中增加一个新的阶段，并对原有阶段进行增强。

### 阶段 1: 引入“研究大纲生成”阶段 (新增)

这个新阶段将发生在“澄清阶段 (Clarification)”之后，但在“迭代研究阶段 (Iterative Research)”之前。

-   **目标**: 根据澄清后的、具体化的研究主题，生成一份结构清晰、逻辑严谨的报告大纲。
-   **实现**:
    1.  创建一个新的服务文件 `services/outline.ts`。
    2.  在该文件中实现一个 `generateOutline(clarifiedQuery: string)` 函数。
    3.  此函数将调用 Gemini 模型，并使用一个专门设计的提示 (Prompt)，要求 AI 扮演一个资深研究员的角色，为给定的主题设计一份全面的报告大纲（类似于 `deep-research` 的 `writeReportPlan`）。
    4.  在 `hooks/useAppLogic.ts` 中，增加一个新的应用状态 `outlining`。
    5.  在澄清阶段结束后，进入 `outlining` 状态，调用 `generateOutline` 函数，并将生成的大纲保存在一个新的 state 变量中，例如 `const [researchOutline, setResearchOutline] = useState<string | null>(null);`。
    6.  (可选) 在 UI (`App.tsx`) 中增加一个区域，用于在生成大纲后向用户展示，并允许用户进行微调或确认。

### 阶段 2: 锚定“规划阶段”

-   **目标**: 确保 Alpha 和 Beta 智能体的规划辩论始终围绕已生成的研究大纲进行。
-   **实现**:
    1.  修改 `services/plannerPrompt.ts`。
    2.  在传递给 Alpha (Strategist) 和 Beta (Tactician) 的系统提示中，**强制性地加入**已生成的 `researchOutline`。
    3.  修改提示语，明确要求它们的辩论和最终生成的搜索查询**必须服务于大纲中的某个具体章节**。例如，可以增加这样的指令：“你们当前的辩论是为了解决研究大纲中‘章节 X.X’的内容。请确保你们的讨论和产出严格限制在此范围内。”

### 阶段 3: 锚定“迭代研究阶段”

-   **目标**: 在每一次研究循环中，都强化研究大纲的指导作用。
-   **实现**:
    1.  修改 `services/research.ts` 中的 `runIterativeDeepResearch` 函数。
    2.  在 `while` 循环的每一次迭代中，传递给规划器 (`runDynamicConversationalPlanner`) 的上下文中，除了当前的研究历史，还必须包含完整的 `researchOutline`。
    3.  AI 在“阅读”和“综合”搜索结果时，其提示也应被修改，要求它将信息与大纲中的特定部分关联起来。

### 阶段 4: 重构“报告生成阶段”

这是最关键的一步，将从根本上改变报告的生成方式。

-   **目标**: 从“自由综合”模式转变为“填充大纲”模式。
-   **实现**:
    1.  修改 `services/synthesis.ts` 中的 `synthesizeReport` 函数。
    2.  修改其函数签名，使其接收 `researchOutline` 作为一个必需的参数。
    3.  重写 `synthesisPrompt.ts` 中的系统提示。新的提示将不再是“请根据以下信息写一份报告”，而是：“请严格按照以下提供的**报告大纲**来撰写最终报告。使用提供的**研究学习点**作为素材，来填充大纲中的每一个章节。确保报告的结构与大纲完全一致，内容与章节标题高度相关。”
    4.  这样，AI 的创造力将被引导到“如何更好地用已有素材填充框架”上，而不是“如何构建报告框架”上，从而极大地减少了偏题的可能性。

## 4. 方案优势

1.  **强约束性**: 研究大纲像一个灯塔，贯穿始终，有效防止了 AI 在迭代过程中的“注意力漂移”。
2.  **结构保证**: 最终报告的结构将与早期规划的大纲高度一致，逻辑性和条理性得到保证。
3.  **保留优点**: 完整保留了 `KResearch` 对话式澄清和动态迭代研究的优点，只是为其增加了一个“骨架”。
4.  **用户透明度**: 用户可以在研究早期就看到报告的雏形（大纲），对最终产出有更明确的预期。

---

请审阅以上方案。如果确认无误，我将开始准备具体的代码实现。