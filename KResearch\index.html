
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>KResearch - AI Deep Research</title>
    <script src="https://cdn.tailwindcss.com?plugins=typography"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'glass-dark': 'rgba(25, 25, 35, 0.2)',
              'glass-light': 'rgba(255, 255, 255, 0.2)',
              'card-bg-light': 'rgba(255, 255, 255, 0.05)',
              'border-dark': 'rgba(255, 255, 255, 0.1)',
              'border-light': 'rgba(255, 255, 255, 0.3)',
              'glow-dark': 'rgba(0, 196, 255, 0.6)',
              'glow-light': 'rgba(0, 122, 255, 0.5)',
              'shadow-dark': 'rgba(0, 0, 0, 0.3)',
              'shadow-light': 'rgba(0, 0, 0, 0.1)',
            },
            boxShadow: {
              'glass': '0 8px 32px 0 var(--tw-shadow-color)',
            },
            typography: (theme) => ({
              DEFAULT: {
                css: {
                  '--tw-prose-body': theme('colors.gray[800]'),
                  '--tw-prose-headings': theme('colors.gray[900]'),
                  '--tw-prose-links': theme('colors.blue[600]'),
                  '--tw-prose-invert-body': theme('colors.gray[300]'),
                  '--tw-prose-invert-headings': theme('colors.gray[100]'),
                  '--tw-prose-invert-links': theme('colors.blue[400]'),
                },
              },
            }),
            animation: {
              'fade-in': 'fadeIn 0.5s ease-in-out forwards',
              'fade-out': 'fadeOut 0.5s ease-in-out forwards',
              'slide-in-right': 'slideInRight 0.5s cubic-bezier(0.25, 0.8, 0.25, 1) forwards',
              'shimmer': 'shimmer 2s infinite linear',
              'spin': 'spin 1s linear infinite',
            },
            keyframes: {
              fadeIn: {
                '0%': { opacity: 0, transform: 'translateY(10px)' },
                '100%': { opacity: 1, transform: 'translateY(0)' },
              },
               fadeOut: {
                'from': { opacity: 1 },
                'to': { opacity: 0 },
              },
              slideInRight: {
                'from': { opacity: 0, transform: 'translateX(100%)' },
                'to': { opacity: 1, transform: 'translateX(0)' },
              },
               shimmer: {
                '0%': { transform: 'translateX(-100%)' },
                '100%': { transform: 'translateX(100%)' },
              },
               spin: {
                'from': { transform: 'rotate(0deg)' },
                'to': { transform: 'rotate(360deg)' },
              },
            },
          },
        },
        darkMode: 'class',
      }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <script>
      mermaid.initialize({ startOnLoad: false, theme: 'default', securityLevel: 'loose' });
    </script>
    <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.11.0",
    "react-markdown": "https://esm.sh/react-markdown@^10.1.0",
    "remark-gfm": "https://esm.sh/remark-gfm@^4.0.1",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react-dom": "https://esm.sh/react-dom@^19.1.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body>
    <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>
