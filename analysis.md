# 深度研究项目分析

本文档旨在深入分析 `KResearch` 和 `deep-research` 这两个项目，详细阐述它们各自的技术架构、核心实现方案和独特功能。

---

## 1. KResearch 项目分析

### 1.1. 项目概述

`KResearch` 是一个先进的、由人工智能驱动的深度研究应用。它利用多智能体系统（Multi-Agent System）来处理复杂的研究课题，通过规划、执行和综合来自网络的信息，最终生成全面的、结构良好的研究报告。该项目旨在为学生、分析师以及任何需要快速深入了解某一主题的用户提供强大支持。其核心特点是其对话式、迭代的研究过程和生成可视化知识图谱的能力。

### 1.2. 技术架构

`KResearch` 采用现代化的前端技术栈，构建了一个纯客户端的单页应用（SPA）。

- **核心框架**: [React](https://react.dev/) 19 + [TypeScript](https://www.typescriptlang.org/)
- **构建工具**: [Vite](https://vitejs.dev/)
- **UI**: [Tailwind CSS](https://tailwindcss.com/)，并采用了流行的 `glassmorphism`（玻璃拟态）设计风格，支持浅色和深色模式。
- **核心依赖**:
  - `@google/genai`: 用于与 Google Gemini API 进行交互。
  - `mammoth`: 用于处理和提取 `.docx` 文件的文本内容。
  - `react-markdown`: 用于在前端渲染 Markdown 格式的报告。
  - `remark-gfm`: 为 `react-markdown` 提供 GitHub Flavored Markdown (GFM) 支持。

由于是纯前端应用，所有的计算和 API 请求都直接从用户的浏览器发出，这在一定程度上增强了数据的私密性。

### 1.3. 核心实现方案

`KResearch` 的研究流程由 `useAppLogic.ts` 这个核心 hook 进行统一调度和状态管理。整个流程被精心设计为多个阶段，每个阶段都有明确的职责：

**1. 澄清阶段 (Clarification):**

- **目的**: 在正式开始深度研究之前，通过与用户进行一到两轮的对话，将模糊或宽泛的研究主题（如“医疗领域的AI”）细化为一个具体、可操作的研究角度。
- **实现**:
  - `services/clarification.ts`: 该文件中的 `clarifyQuery` 函数负责此阶段的逻辑。
  - 它会调用 Gemini 模型，分析用户的初始查询和对话历史，生成一个引导性的问题，帮助用户缩小范围。
  - 如果用户提供了足够的信息，或者跳过此步骤，AI 会生成一个研究主题的摘要，作为后续研究的指导。
- **独特性**: 这是 `KResearch` 的一个标志性功能，通过前置的交互显著提升了后续研究的准确性和相关性。

**2. 规划阶段 (Planning):**

- **目的**: 创建一个结构化、高效的研究计划。
- **实现**:
  - `services/planner.ts`: 实现了独特的“动态对话式规划器”（Dynamic Conversational Planner）。
  - 该规划器模拟了两个 AI 智能体（Agent）的协作：
    - **Alpha (Strategist)**: 负责制定高层次的研究方向和策略。
    - **Beta (Tactician)**: 负责将策略转化为具体的、可执行的搜索查询。
  - 两个智能体通过多轮“辩论”来迭代和优化研究计划，确保计划的全面性和深度。
  - `services/plannerPrompt.ts`: 定义了驱动这两个智能体进行对话和决策的复杂提示（Prompt）。

**3. 迭代研究阶段 (Iterative Research):**

- **目的**: 根据规划阶段生成的查询，执行搜索、阅读和信息提取。
- **实现**:
  - `services/research.ts`: `runIterativeDeepResearch` 函数是此阶段的核心。
  - 它以循环方式执行以下操作：
    1.  调用规划器（`planner.ts`）获取下一步的搜索查询。
    2.  调用 `services/search.ts` 中的 `executeSingleSearch` 函数，使用 Google Search API 执行搜索。
    3.  对搜索结果进行“阅读”和“综合”，提取关键信息和学习点。
  - 这个过程会持续进行，直到满足预设的最小循环次数，或者规划器决定研究已经足够深入并可以结束。

**4. 综合与报告生成阶段 (Synthesis):**

- **目的**: 将所有收集到的信息和学习点综合成一份结构化的 Markdown 报告。
- **实现**:
  - `services/synthesis.ts`: `synthesizeReport` 函数负责生成最终报告。
  - 它会接收整个研究历史（包括所有“thought”、“search”、“read”的更新）和引文列表。
  - AI 会根据一个强大的系统提示（Prompt），将零散的信息组织成一篇连贯、详细的报告。
  - `FinalReport.tsx` 组件负责在前端展示这份 Markdown 报告，并提供了复制、重新生成、重写和翻译等交互功能。

**5. 可视化报告生成 (Visualization):**

- **目的**: 将 Markdown 报告转化为一个交互式的、视觉丰富的单页 HTML 报告。
- **实现**:
  - `services/visualizer.ts`: `generateVisualReport` 函数是此功能的亮点。
  - 它向 AI 发送一个特殊的提示，要求其扮演一个世界级的 UI/UX 设计师和前端开发者，将 Markdown 内容重新想象和创作为符合“液体玻璃”主题的 HTML 页面。
  - AI 会生成包含内联 CSS 和 JavaScript 的完整 HTML 代码，包括自定义的 SVG 图标和图表，以实现丰富的视觉效果。
  - `ReportVisualizer.tsx` 组件则在一个模态框中渲染这个 HTML 报告。

### 1.4. 独特功能

- **多智能体协作规划**: Alpha 和 Beta 智能体的辩论机制是一种非常新颖和强大的规划方式。
- **对话式查询澄清**: 在研究开始前与用户交互，确保研究方向的准确性。
- **交互式可视化报告**: 将纯文本报告一键转化为精美的 HTML 页面，极大地提升了报告的可读性和吸引力。
- **版本控制与再创作**: 支持对生成的报告进行多次重写、重新生成和翻译，并将每次的结果保存为不同版本，方便用户对比和选择。
- **可定制的角色 (Roles)**: 用户可以创建和管理不同的 AI 角色，每个角色都有独特的指令和个性，从而从不同视角（如“魔鬼代言人”、“技术分析师”）进行研究。

---

## 2. deep-research 项目分析

### 2.1. 项目概述

`deep-research` 是一个旨在快速生成深度研究报告的应用程序。它利用强大的“思考”和“任务”模型，结合互联网搜索能力，在几分钟内提供对各种主题的快速而富有洞察力的分析。该项目强调隐私，所有数据都在本地处理和存储。它支持多种大型语言模型（LLM）和搜索引擎，并提供了通过 SSE API 作为SaaS服务使用的能力。

### 2.2. 技术架构

`deep-research` 采用 Next.js 框架，使其既可以作为传统的服务器端渲染（SSR）应用，也可以作为静态站点（SSG）或通过 `standalone` 模式部署。

- **核心框架**: [Next.js](https://nextjs.org/) 15 + [TypeScript](https://www.typescriptlang.org/)
- **UI**: [Tailwind CSS](https://tailwindcss.com/) + [Shadcn UI](https://ui.shadcn.com/)
- **状态管理**: [Zustand](https://zustand-demo.pmnd.rs/)
- **核心依赖**:
  - `@ai-sdk/*`: Vercel AI SDK 的一系列库，用于与不同的 LLM Provider（如 Google, OpenAI, Anthropic）进行交互。
  - `marked`: 用于将 Markdown 转换为 HTML。
  - `mermaid`: 用于在前端渲染知识图谱。
  - `localforage`: 用于在浏览器中进行本地数据存储（如研究历史）。

其架构的核心是后端的 API 路由（位于 `src/app/api/`），特别是 `sse/route.ts`，它通过 Server-Sent Events (SSE) 将研究过程的实时更新流式传输到前端。

### 2.3. 核心实现方案

`deep-research` 的研究流程是线性的、分阶段的，主要由 `src/utils/deep-research/index.ts` 中的 `DeepResearch` 类来驱动。

**1. 撰写报告方案 (Write Report Plan):**

- **目的**: 根据用户的初始查询，生成一个结构化的报告大纲。
- **实现**:
  - `writeReportPlan` 方法接收用户查询，并使用一个预设的提示（`reportPlanPrompt`）来调用“思考模型”（Thinking Model）。
  - AI 会返回一个 Markdown 格式的报告大纲，这个大纲将指导后续的信息收集。

**2. 生成 SERP 查询 (Generate SERP Queries):**

- **目的**: 将报告方案分解为一系列具体的搜索引擎查询。
- **实现**:
  - `generateSERPQuery` 方法接收上一步生成的报告方案。
  - 它使用 `generateSerpQueriesPrompt` 提示，要求 AI 将方案的每个部分转化为具体的搜索问题，并以 JSON 格式返回。

**3. 运行搜索任务 (Run Search Task):**

- **目的**: 执行生成的搜索查询，并从结果中提取关键信息。
- **实现**:
  - `runSearchTask` 方法接收一个任务列表。
  - 它会并行地（通过 `p-limit` 控制并发数）为每个任务执行搜索。
  - **搜索源**:
    - 如果配置为 `model`，它会利用 AI 模型内置的搜索能力（如 Gemini 的 `useSearchGrounding` 或 GPT-4o 的 `web_search_preview`）。
    - 如果配置为第三方搜索引擎（如 Tavily, Searxng），它会调用 `utils/deep-research/search.ts` 中的 `createSearchProvider` 来执行搜索。
  - 搜索完成后，AI 会使用 `processResultPrompt` 或 `processSearchResultPrompt` 来处理和总结搜索结果，形成“学习点”（learnings）。

**4. 撰写最终报告 (Write Final Report):**

- **目的**: 将所有阶段的产出（报告方案、学习点、引文）综合成最终的 Markdown 报告。
- **实现**:
  - `writeFinalReport` 方法是最后一步。
  - 它整合所有信息，并使用一个非常详细的 `writeFinalReportPrompt` 来调用“思考模型”。
  - 这个提示要求 AI 严格按照报告方案的结构，用收集到的学习点来填充内容，并正确地引用来源和图片。
  - `components/Research/FinalReport/index.tsx` 组件负责渲染这份报告，并提供了一系列后期处理工具。

**5. 知识图谱生成 (Knowledge Graph):**

- **目的**: 从生成的报告中提取关键实体及其关系，并以图形方式展示。
- **实现**:
  - 在最终报告生成后，用户可以点击按钮触发此功能。
  - `components/Research/FinalReport/KnowledgeGraph.tsx` 组件会调用 AI，使用 `knowledgeGraphPrompt` 提示。
  - AI 分析报告内容，并生成 [Mermaid.js](https://mermaid.js.org/) 格式的代码，前端再使用 Mermaid.js 库将其渲染成可视化的知识图谱。

### 1.4. 独特功能

- **多模型和多搜索引擎支持**: `deep-research` 的架构设计使其可以轻松接入多种 LLM（通过 Vercel AI SDK）和搜索引擎。
- **服务器端流式传输 (SSE)**: 通过 SSE API，研究过程的每一步都可以实时地流式传输到前端，提供了良好的实时反馈。
- **本地知识库**: 支持用户上传文件（文本文档、Office 文件、PDF），这些文件会作为研究的本地信息源。
- **强大的后期编辑功能 (Artifact)**: 提供了丰富的报告后期处理工具，如调整阅读水平、文章长度、全文翻译和 AI 续写等。
- **模块化和可扩展性**: 提供了 MCP (Model Context Protocol) 服务器实现，允许其他 AI 服务将其作为一种能力进行调用。

---
