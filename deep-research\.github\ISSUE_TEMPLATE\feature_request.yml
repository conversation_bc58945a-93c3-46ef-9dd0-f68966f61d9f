name: Feature request
description: Suggest an idea for this project
title: "[Feature Request]: "
labels: ["feature"]

body:
  - type: markdown
    attributes:
      value: "## Is your feature request related to a problem? Please describe."
  - type: textarea
    id: problem-description
    attributes:
      label: Problem Description
      description: "A clear and concise description of what the problem is. Example: I'm always frustrated when [...]"
      placeholder: "Explain the problem you are facing..."
    validations:
      required: true

  - type: markdown
    attributes:
      value: "## Describe the solution you'd like"
  - type: textarea
    id: desired-solution
    attributes:
      label: Solution Description
      description: A clear and concise description of what you want to happen.
      placeholder: "Describe the solution you'd like..."
    validations:
      required: true

  - type: markdown
    attributes:
      value: "## Describe alternatives you've considered"
  - type: textarea
    id: alternatives-considered
    attributes:
      label: Alternatives Considered
      description: A clear and concise description of any alternative solutions or features you've considered.
      placeholder: "Describe any alternative solutions or features you've considered..."
    validations:
      required: false

  - type: markdown
    attributes:
      value: "## Additional context"
  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Add any other context or screenshots about the feature request here.
      placeholder: "Add any other context or screenshots about the feature request here..."
    validations:
      required: false
