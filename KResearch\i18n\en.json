{"langCode": "en", "history": "History", "settings": "Settings", "github": "View on GitHub", "appTagline": "Your AI-powered deep research assistant.", "modeBalanced": "Balanced", "modeBalancedDesc": "Optimal mix for quality and speed.", "modeDeepDive": "Deep Dive", "modeDeepDiveDesc": "Highest quality using the most powerful models.", "modeFast": "Fast", "modeFastDesc": "Quick results with capable models.", "modeUltraFast": "Ultra Fast", "modeUltraFastDesc": "Lightning-fast results for quick checks.", "advancedSearch": "Advanced: Guide Initial Search", "guidedSearchPlaceholder": "Initial search topics, one per line...", "mainQueryPlaceholder": "What is the future of AI in healthcare? (You can also attach a file)", "attachFile": "Attach file", "removeFile": "Remove file", "startResearch": "Start Research", "stopResearch": "Stop Research", "continueResearch": "Continue Research", "generateReport": "Generate Report", "generatingReport": "Generating Report...", "toggleResearchLog": "{{action}} Research Log", "show": "Show", "hide": "<PERSON>de", "startNewResearch": "Start New Research", "footerPoweredBy": "Powered by Gemini.", "refiningRequest": "Refining Your Request", "refiningRequestDesc": "To get the best results, the AI may ask a few clarifying questions.", "clarificationPlaceholder": "Your answer...", "clarificationPlaceholderInitial": "Please provide the research topic you would like to refine.", "sendAnswer": "Send Answer", "skipAndStart": "Skip & Start Research", "thinking": "Thinking...", "waiting": "Waiting...", "researchHistory": "Research History", "close": "Close", "searchHistory": "Search history...", "noMatchingHistory": "No matching history found.", "tryDifferentSearch": "Try a different search term.", "historyAppearsHere": "Completed research will appear here.", "editTitle": "Edit Title", "delete": "Delete", "load": "Load", "clearAllHistory": "Clear All History", "researching": "Researching...", "researchComplete": "Research Complete", "searchingFor": "Searching For:", "readAndSynthesized": "Read & Synthesized", "sources": "sources", "thought": "Thought", "outline": "Outlining Report", "agentAlpha": "Agent Alpha", "agentBeta": "Agent <PERSON>", "visualizingReport": "Agent is generating your visual report...", "noVisualReport": "No visual report generated yet.", "apiKey": "API Key", "parameters": "Parameters", "models": "Models", "restoreDefaults": "<PERSON><PERSON>", "dark": "Dark", "light": "Light", "citations": "Citations", "untitledSource": "Untitled Source", "finalReport": "Final Report", "copied": "Copied!", "failed": "Failed!", "copy": "Copy", "reportOnly": "Report Only", "reportAndCitations": "Report & Citations", "regenerating": "Regenerating", "success": "Success!", "regenerateReport": "Regenerate Report", "visualizing": "Visualizing...", "visualize": "Visualize", "researchSummary": "Research Summary", "researchTime": "Research Time", "sourcesFound": "Sources Found", "searchCycles": "Search Cycles", "translate": "Translate", "addEmojis": "Add <PERSON>s", "addFinalPolish": "Add Final Polish", "readingLevel": "Reading Level", "adjustLength": "Adjust Length", "customEdit": "Custom Edit", "adjustLengthTitle": "Adjust Length", "muchShorter": "<PERSON> Shorter", "shorter": "Shorter", "longer": "Longer", "muchLonger": "<PERSON> Longer", "readingLevelTitle": "Reading Level", "kindergarten": "Kindergarten", "middleSchool": "Middle School", "highSchool": "High School", "college": "College", "graduateSchool": "Graduate School", "addEmojisTitle": "Add <PERSON>s", "emojisToWords": "To Words", "emojisToSections": "To Sections", "emojisToLists": "To Lists", "emojisRemoveAll": "Remove All", "customEditTitle": "Custom Edit", "customEditPlaceholder": "e.g., Rewrite this in a more casual tone, using the attached file for context.", "attach": "Attach", "apply": "Apply", "translateReportTitle": "Translate Report", "language": "Language", "languagePlaceholder": "e.g., Chinese, Malay", "style": "Style", "colloquial": "Colloquial", "literal": "Literal", "translating": "Translating...", "geminiApiKeys": "Gemini API Key(s)", "apiKeysConfiguredByHost": "API Key(s) are configured by the application host.", "apiKeysPlaceholder": "Enter your Gemini API Key(s), one per line.", "apiBaseUrl": "API Base URL (Optional)", "apiBaseUrlDesc": "Change this only if you need to use a proxy or a different API endpoint.", "apiBaseUrlPlaceholder": "e.g., https://generativelanguage.googleapis.com", "modelConfig": "Model Configuration", "refreshModelList": "Refresh Model List", "loading": "Loading...", "modelConfigDesc": "Override default models for each agent. Select \"Default\" to use the model specified by the current research mode.", "defaultModel": "<PERSON><PERSON><PERSON>", "researchParams": "Research Parameters", "minCycles": "Min Research Cycles", "minCyclesHelp": "Minimum cycles before finishing.", "maxCycles": "Max Research Cycles", "maxCyclesHelp": "Hard limit for research iterations.", "maxDebateRounds": "Max Debate Rounds", "maxDebateRoundsHelp": "Agent planning conversation length.", "uncapped": "Uncapped", "feedback": "<PERSON><PERSON><PERSON>", "giveFeedback": "<PERSON>", "cancel": "Cancel", "feedbackPlaceholder": "e.g., 'Make the charts blue' or 'Add a section about financial impact.'", "generateNewVersion": "Generate New Version", "feedbackSuccess": "Success! Regenerating...", "feedbackError": "Update failed. Try again.", "visualReport": "Visual Report", "regenerate": "Regenerate", "download": "Download", "changeLanguage": "Change language", "apiKeyRequiredTitle": "API Key Required", "apiKeyRequiredMessage": "Please set your Gemini API key in the settings before starting research.", "emptyQueryTitle": "Empty Query", "emptyQueryMessage": "Cannot start research with an empty query.", "initialSearchFailedTitle": "Initial Search Failed", "allApiKeysFailedTitle": "All API Keys Failed", "allApiKeysFailedMessage": "You can retry the operation or check your keys in Settings.", "researchStoppedTitle": "Research Stopped", "researchStoppedMessage": "The research process was cancelled by the user.", "researchFailedTitle": "Research Failed", "clarificationFailedTitle": "Clarification Failed", "apiKeysFailedMessage": "All API keys failed. You can retry the operation.", "clarifiedContextFailed": "Clarification process failed. Proceeding with original query.", "generatingOutlineTitle": "Generating Outline", "generatingOutlineMessage": "Creating a structure for the final report.", "reportGeneratedTitle": "Report Generated", "reportGeneratedMessage": "Report generated from the research completed so far.", "synthesisFailedTitle": "Synthesis Failed", "synthesisFailedMessage": "Synthesis failed. Please check your keys in Settings.", "visualizationFailedTitle": "Visualization Failed", "regeneratingOutlineTitle": "Generating New Outline", "regeneratingOutlineMessage": "Re-structuring report before regeneration.", "reportRegeneratedTitle": "Report Regenerated", "reportRegeneratedMessage": "A new version of the report has been generated.", "regenerationFailedTitle": "Regeneration Failed", "reportUpdatedTitle": "Report Updated", "reportUpdatedMessage": "The report has been successfully rewritten.", "rewriteFailedTitle": "Rewrite Failed", "historyItemRemovedTitle": "History item removed", "historyItemRemovedMessage": "The selected item has been deleted from your research history.", "historyClearedTitle": "History cleared", "historyClearedMessage": "All items have been removed from your research history.", "translationCompleteTitle": "Translation Complete", "translationCompleteMessage": "Report translated and saved as a new version.", "translationFailedTitle": "Translation Failed", "defaultsLoadedTitle": "Defaults Loaded", "defaultsLoadedMessage": "Settings have been reset to default and saved.", "roles": "Roles", "selectRole": "Select Role", "defaultRole": "<PERSON><PERSON><PERSON>", "defaultRoleDesc": "Standard research agent.", "builtIn": "Built-in", "custom": "Custom", "manageRoles": "Manage Roles", "editRole": "Edit Role", "createNewRole": "Create New Role", "edit": "Edit", "roleNamePlaceholder": "Role Name...", "rolePromptPlaceholder": "Describe the persona and instructions for this role. e.g., 'Act as a skeptical financial analyst. Focus on risks and financial viability...'", "generateNameEmoji": "Generate", "refinePrompt": "Refine", "creativePrompt": "Creative", "attachContextFile": "Attach Context File", "saveRole": "Save Role", "confirmDeleteRole": "Are you sure you want to delete this role?", "roleAI": "Role AI"}