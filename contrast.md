# KResearch vs. deep-research 对比分析

本文档旨在对 `KResearch` 和 `deep-research` 两个项目进行详细的横向对比，重点分析它们在核心理念、技术实现、研究流程以及最终报告生成方法上的异同。

---

### 1. 核心理念与定位对比

| 特性 | KResearch | deep-research |
| :--- | :--- | :--- |
| **核心定位** | AI驱动的**对话式**深度研究助手 | 闪电般快速的**自动化**深度研究报告生成器 |
| **交互模式** | 强调在研究开始前通过与用户的**对话**来澄清和细化研究方向。 | 更偏向于**一键式**自动化流程，用户输入主题后，系统自主完成后续所有步骤。 |
| **研究哲学** | 采用**迭代和辩论**的模式，通过多智能体（Alpha/Beta）的协作来动态规划和调整研究路径，追求过程的严谨和最优。 | 采用**线性、分阶段**的流水线模式（规划->搜索->综合），追求流程的高效和快速。 |
| **隐私侧重** | **纯客户端应用**，所有 API 请求直接从浏览器发出，数据完全保留在用户本地。 | **客户端 + 后端 API** 架构，支持本地模式和服务器代理模式，提供 SSE 流式传输和作为 SaaS 服务的能力。 |

**总结**: `KResearch` 更像一个与用户协作的“研究伙伴”，强调通过交互来提升研究质量和方向的准确性。而 `deep-research` 更像一个高效的“自动化工具”，旨在以最快的速度产出一份高质量的报告。

---

### 2. 技术栈与架构对比

| 技术栈 | KResearch | deep-research |
| :--- | :--- | :--- |
| **前端框架** | React 19 + Vite | Next.js 15 |
| **UI 库** | 自定义组件 + Tailwind CSS (Glassmorphism) | Shadcn UI + Tailwind CSS |
| **状态管理** | React Hooks (useState, useCallback) | Zustand |
| **AI SDK** | `@google/genai` (直接调用 Gemini) | Vercel AI SDK (`@ai-sdk/*`) |
| **部署模型** | 纯静态客户端应用 | SSR, SSG, Standalone, Docker, PWA |
| **核心架构** | 单页应用 (SPA)，所有逻辑在 `useAppLogic.ts` 中 | 客户端 + 后端 API 路由 (`/api/*`) |

**总结**: `KResearch` 的架构更简单、轻量，是一个纯粹的前端应用。而 `deep-research` 的架构则更复杂、更强大，其 Next.js 后端使其具备了提供 API 服务、处理代理请求等高级功能，可扩展性更强。

---

### 3. 研究流程对比

两个项目的研究流程在宏观上相似，都包含“澄清/规划”、“搜索/执行”、“综合/报告”三个阶段，但具体实现上存在显著差异。

#### 3.1. 规划与澄清阶段

- **KResearch**: **对话式澄清 + 双智能体辩论规划**
  - **澄清**: 在研究开始前，通过 `clarifyQuery` 与用户进行1-2轮对话，将宽泛的主题具体化。这是一个独特的前置交互步骤。
  - **规划**: 使用 `runDynamicConversationalPlanner`，模拟 Alpha (战略家) 和 Beta (战术家) 两个 AI 智能体的对话。它们通过多轮辩论，共同制定和优化搜索查询，整个过程是动态和迭代的。

- **deep-research**: **线性规划**
  - **规划**: 用户输入主题后，系统首先通过 `writeReportPlan` 直接生成一份完整的报告大纲。
  - **查询生成**: 接着，`generateSERPQuery` 方法会将这份大纲分解成一系列具体的 SERP (搜索引擎结果页) 查询任务。
  - **流程**: 这是一个线性的、从上至下的流程，缺乏动态调整和迭代优化的环节。

**对比**: `KResearch` 的规划过程更智能、更具适应性，能够根据研究的进展动态调整策略。而 `deep-research` 的规划过程更直接、更高效，但相对固定。

#### 3.2. 信息收集与执行阶段

- **KResearch**: **迭代式搜索与阅读**
  - 在 `runIterativeDeepResearch` 的 `while` 循环中，不断地调用规划器获取新查询，然后执行搜索和阅读，将新的“学习点”反馈给规划器，形成一个闭环。这个过程会持续多个“研究周期”。

- **deep-research**: **批量任务执行**
  - `runSearchTask` 方法接收一个任务列表，并可以并行执行这些搜索任务。它一次性完成所有预定的搜索，并将结果汇总。

**对比**: `KResearch` 的信息收集是**迭代式**的，每一步都为下一步提供参考。`deep-research` 的信息收集是**批量式**的，一次性完成所有规划好的任务，效率更高。

---

### 4. 最终报告生成方法对比

这是两个项目差异最显著的地方之一，体现了它们不同的产品哲学。

- **KResearch**: **综合 + 可视化再创作**
  1.  **综合 (`synthesizeReport`)**: 首先，将研究过程中所有的“学习点”和历史记录喂给 AI，生成一份详尽的、结构化的 Markdown 报告。这一步的重点是**内容的全面性和准确性**。
  2.  **可视化 (`generateVisualReport`)**: 这是 `KResearch` 的“杀手级功能”。它并不满足于纯文本的 Markdown，而是将生成的报告作为**原材料**，再次调用 AI，要求其扮演“UI/UX 设计师”，将报告**重新创作**成一个视觉效果丰富、交互性强的单页 HTML 应用。这个过程强调的是**信息的表现力**和**美学价值**。
  3.  **后期处理**: 用户还可以对生成的报告进行多次重写、翻译和版本管理，提供了极大的灵活性。

- **deep-research**: **直接生成 + 后期编辑**
  1.  **直接生成 (`writeFinalReport`)**: `deep-research` 的报告生成是一步到位的。它将报告方案、所有学习点、引文等信息一次性提供给 AI，并使用一个非常详尽和结构化的提示，直接生成最终的 Markdown 报告。这个过程的提示工程（Prompt Engineering）非常复杂，力求一次性得到高质量的输出。
  2.  **知识图谱**: 作为对报告内容的补充和可视化，`deep-research` 提供了从最终报告中提取实体和关系，并生成 Mermaid.js 知识图谱的功能。这是一种对已有内容的**结构化提取**，而非重新创作。
  3.  **后期编辑 (Artifact)**: 提供了强大的后期编辑工具，如调整阅读水平、长度、翻译等，这些都是对已生成的 Markdown 内容进行**再加工**。

**核心差异总结**: 

- **生成流程**: `KResearch` 是**两步式**的（内容生成 -> 视觉再创作），而 `deep-research` 是**一步式**的（直接生成最终报告）。
- **最终产物**: `KResearch` 的最终交付物是一个**交互式 HTML 页面**，强调视觉体验。`deep-research` 的最终交付物是一份**高质量的 Markdown 文档**，强调内容的结构化和可编辑性。
- **可视化理念**: `KResearch` 的可视化是**表现层**的再创作，追求美学和交互。`deep-research` 的知识图谱是**内容层**的结构化提取，追求逻辑和关系的可视化。

### 5. 结论

`KResearch` 和 `deep-research` 虽然目标相似，但实现路径和侧重点截然不同。

- **KResearch** 更适合那些希望深入参与研究过程、通过与 AI 协作来打磨研究方向和细节的用户。它的动态规划和可视化报告生成能力使其在**研究体验**和**结果呈现**上独树一帜。

- **deep-research** 更适合那些追求效率、希望快速获得一份结构完整、内容详实的自动化报告的用户。它的多模型/多搜索引擎支持、强大的后端架构和丰富的后期编辑功能使其成为一个高度可扩展和实用的**生产力工具**。
