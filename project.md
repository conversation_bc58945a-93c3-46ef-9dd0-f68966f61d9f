# KResearch 学术综述论文格式优化方案

## 1. 项目现状分析

### 1.1 当前问题诊断

基于对KResearch项目的深入分析，识别出以下核心问题：

**1. 报告跑题问题的根本原因**
- **上下文漂移**：在多轮迭代搜索中，AI注意力被非核心细节吸引，逐渐偏离原始主题
- **缺乏结构约束**：双智能体辩论虽然灵活，但缺乏顶层设计作为强约束
- **综合阶段信息过载**：AI需要处理大量零散学习点，缺乏清晰的结构指引

**2. 输出内容过短不详细的技术原因**
- **综合提示词缺陷**：没有明确的长度要求，仅依赖AI自主判断
- **搜索深度不足**：仅使用Google Search摘要信息，缺乏深度内容
- **学术标准缺失**：缺乏学术论文的严格结构和深度要求

**3. 当前报告生成逻辑的局限性**
- **格式不规范**：缺乏学术综述论文的标准结构
- **引用不严格**：缺乏规范的引用格式和参考文献管理
- **质量控制不足**：缺乏内容深度和学术严谨性的保证机制

### 1.2 与deep-research的对比优势分析

**deep-research的优势**：
- 明确要求"aim for 5 pages or more, the more the better"
- 要求"include ALL the learnings from research"
- 有专门的报告计划阶段，结构更清晰
- 支持多种搜索引擎，获取更详细内容
- 有明确的引用和图片集成要求

**KResearch需要借鉴的要素**：
- 结构化的报告规划机制
- 明确的内容长度和深度要求
- 更丰富的搜索数据源
- 严格的引用和参考文献格式

## 2. 学术综述论文格式优化方案

### 2.1 核心设计理念

将KResearch的最终报告从"一般性研究报告"升级为"学术综述论文格式"，**同时完全保留双智能体辩论机制的核心优势**，具体包括：

1. **标准学术结构**：摘要、引言、文献综述、分析讨论、结论、参考文献
2. **严格引用格式**：采用APA或IEEE引用标准
3. **内容深度保证**：最低8000字，包含详细的理论分析和实证讨论
4. **学术严谨性**：确保逻辑严密、论证充分、结论可靠
5. **保留协作优势**：Alpha/Beta智能体在学术大纲设计、搜索规划、内容综合等各阶段继续发挥协作作用

### 2.2 技术实现方案（强化双智能体协作）

#### 阶段1：引入学术大纲生成机制（保留双智能体辩论）

**目标**：在澄清阶段后，生成符合学术综述论文标准的详细大纲，同时保留Alpha/Beta智能体的协作优势

**实现步骤**：

1. **创建新服务文件** `services/academicOutline.ts`
```typescript
export const generateAcademicOutline = async (
    clarifiedQuery: string,
    mode: ResearchMode,
    signal?: AbortSignal
): Promise<string> => {
    // 使用双智能体协作生成学术大纲
    // Alpha负责整体结构设计，Beta负责细节完善
}
```

2. **设计双智能体学术大纲协作提示词**
```typescript
const academicOutlinePrompt = `
你是Agent ${persona}，正在与另一位专家协作设计学术综述论文大纲。

研究主题：${clarifiedQuery}

${persona === 'Alpha' ? `
作为首席学术策略专家，你的职责：
1. 设计符合学术标准的整体论文结构
2. 确定主要研究问题和理论框架
3. 规划各章节的逻辑关系和重点
` : `
作为学术执行专家，你的职责：
1. 完善Alpha提出的结构框架
2. 细化各章节的具体内容要点
3. 确保学术写作的规范性和可操作性
`}

协作要求：
1. 采用标准学术论文结构：摘要、引言、文献综述、主体分析、结论、参考文献
2. 每个章节需要包含3-5个子章节，确保内容的全面性和深度
3. 明确每个章节的研究重点和预期内容长度
4. 确保逻辑结构严密，章节间有清晰的递进关系
5. 总体目标：生成8000-12000字的高质量学术综述

通过辩论优化大纲，确保学术严谨性和内容完整性。
`;
```

3. **修改应用状态管理**
```typescript
// 在useAppLogic.ts中添加新状态
const [academicOutline, setAcademicOutline] = useState<string | null>(null);
const [appState, setAppState] = useState<AppState>('idle'); // 添加'outlining'状态
```

#### 阶段2：增强搜索机制（保留智能体协作）

**目标**：获取更深度、更学术化的搜索内容，同时保留Alpha/Beta智能体在搜索规划中的协作优势

**实现方案**：

1. **增强双智能体搜索规划**
```typescript
// 修改plannerPrompt.ts，加入学术搜索指导
const enhancedPlannerPrompt = `
你是Agent ${nextPersona}，正在协作制定学术研究的搜索策略。

当前学术大纲：
${academicOutline}

${nextPersona === 'Alpha' ? `
作为学术策略专家，重点关注：
1. 为大纲各章节制定针对性的搜索策略
2. 识别需要深入研究的理论框架和概念
3. 规划学术文献和权威资料的搜索方向
` : `
作为学术执行专家，重点关注：
1. 将Alpha的策略转化为具体的搜索查询
2. 确保搜索查询能获取高质量的学术资料
3. 优化查询词以获取权威机构和专家观点
`}

搜索要求：
- 重点关注学术文献、研究报告、权威机构资料
- 每个查询应针对大纲中的特定章节
- 确保搜索结果的学术价值和权威性
`;
```

2. **扩展搜索提示词**
```typescript
const enhancedSearchPrompt = `
请针对查询"${searchQuery}"进行深度学术搜索和分析。
此搜索服务于学术大纲的"${targetSection}"章节。

要求：
1. 重点关注学术文献、研究报告、权威机构发布的资料
2. 提供详细的背景信息、理论基础和实证数据
3. 包含相关的统计数据、案例研究和专家观点
4. 确保信息的权威性和学术价值
5. 每个搜索结果应提供至少500字的详细摘要

输出格式：
- 详细的学术背景介绍
- 关键理论和概念解释
- 相关研究发现和数据
- 专家观点和权威引用
- 实际应用案例和影响分析
`;
```

#### 阶段3：重构综合报告生成

**目标**：生成符合学术综述论文标准的高质量报告

**核心改进**：

1. **新的综合提示词设计**
```typescript
const academicSynthesisPrompt = `
你是一位顶级的学术研究专家和论文写作大师。请根据提供的学术大纲和研究资料，撰写一篇高质量的学术综述论文。

学术大纲：
${academicOutline}

研究资料：
${learnings}

严格要求：
1. **结构严谨**：严格按照提供的学术大纲撰写，确保每个章节内容充实
2. **长度要求**：总字数不少于8000字，每个主要章节不少于1500字
3. **学术标准**：
   - 摘要：300-500字，包含研究背景、方法、主要发现、结论
   - 引言：1000-1500字，阐述研究背景、意义、目标和结构
   - 文献综述：2000-3000字，系统梳理相关研究
   - 主体分析：3000-4000字，深入分析和讨论
   - 结论：800-1200字，总结发现、贡献和未来方向
4. **引用规范**：使用APA格式，确保每个重要观点都有可靠来源
5. **学术语言**：使用正式的学术写作风格，逻辑清晰，论证严密
6. **内容深度**：
   - 提供详细的理论分析和实证讨论
   - 包含多角度的批判性思考
   - 展示深入的专业洞察和独到见解
   - 确保论证充分、数据支撑有力

输出要求：
- 完整的Markdown格式学术论文
- 规范的章节标题和编号
- 详细的参考文献列表
- 专业的学术图表（如需要）
`;
```

2. **实现分章节生成机制**
```typescript
const generateAcademicReport = async (
    outline: string,
    learnings: string[],
    citations: Citation[]
): Promise<string> => {
    // 按章节分别生成，确保每个章节的质量和深度
    // 最后整合成完整的学术论文
}
```

#### 阶段4：建立质量保证机制

**目标**：确保生成的学术论文达到高质量标准

**实现方案**：

1. **内容质量检查**
```typescript
const validateAcademicQuality = (report: string): {
    isValid: boolean;
    issues: string[];
    suggestions: string[];
} => {
    // 检查字数、结构、引用格式等
}
```

2. **防跑题机制**
```typescript
const checkTopicRelevance = (
    report: string,
    originalQuery: string,
    outline: string
): number => {
    // 计算报告与原始主题的相关性得分
}
```

3. **学术规范检查**
```typescript
const validateAcademicStandards = (report: string): {
    citationCount: number;
    structureCompliance: boolean;
    languageQuality: number;
} => {
    // 检查学术写作规范
}
```

### 2.3 用户界面优化

**新增功能**：

1. **学术大纲预览**：在研究开始前展示详细的学术大纲
2. **进度指示器**：显示各章节的完成进度和质量指标
3. **质量评估面板**：实时显示报告的学术质量评分
4. **引用管理器**：提供规范的引用格式和参考文献管理

## 3. 实施计划

### 3.1 开发阶段

**第一阶段（1-2周）**：
- 实现学术大纲生成功能
- 设计新的提示词模板
- 修改应用状态管理

**第二阶段（2-3周）**：
- 增强搜索机制
- 重构综合报告生成
- 实现分章节生成逻辑

**第三阶段（1-2周）**：
- 建立质量保证机制
- 优化用户界面
- 进行全面测试

### 3.2 质量验证

**验证标准**：
1. 生成报告字数达到8000字以上
2. 学术结构完整，符合综述论文标准
3. 引用格式规范，参考文献完整
4. 内容与主题高度相关，无明显跑题
5. 学术语言规范，逻辑严密

**测试方案**：
- 使用多个不同领域的研究主题进行测试
- 邀请学术专家进行质量评估
- 与现有版本进行对比分析

## 4. 预期效果

通过本优化方案的实施，KResearch将能够：

1. **生成高质量学术综述论文**：符合学术标准，结构完整，内容深入
2. **有效防止报告跑题**：通过学术大纲约束，确保内容聚焦
3. **显著提升内容深度**：最低8000字，包含详细分析和讨论
4. **建立学术写作标准**：规范的引用格式和参考文献管理
5. **提供专业研究工具**：满足学术研究和论文写作需求
6. **保持协作优势**：继续发挥Alpha/Beta智能体辩论机制的独特价值，在学术研究中实现更高质量的协作

**核心优势保留**：
- Alpha/Beta智能体的动态辩论机制完全保留
- 在学术大纲设计、搜索规划、内容分析等各环节继续发挥协作优势
- 通过学术框架约束，让智能体辩论更加聚焦和深入
- 实现"结构化约束 + 动态协作"的最佳平衡

这将使KResearch从一个通用的研究工具升级为专业的学术研究助手，既保持了原有的智能协作特色，又能为用户提供真正有价值的学术综述论文。
